<?php

declare(strict_types=1);

namespace Comave\EmailConfig\Plugin;

use Comave\EmailConfig\Model\Config\Config;
use Magento\Email\Model\Template\Filter;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Framework\App\DeploymentConfig;
use Psr\Log\LoggerInterface;

/**
 * Plugin to intercept URL generation in email templates and use frontend URLs
 */
class EmailTemplateUrlPlugin
{
    private const BACKEND_DOMAINS = [
        'mcstaging.comave.com',
        'mcproduction.comave.com',
        'mcdevelopment.comave.com',
        'mc.comave.com',
        'comave-magento.ddev.site',
        'mcdev.comave.com'
    ];

    private const FRONTEND_DOMAIN_MAPPING = [
        'mcstaging.comave.com' => 'staging.comave.com',
        'mcproduction.comave.com' => 'comave.com',
        'mcdevelopment.comave.com' => 'development.comave.com',
        'mc.comave.com' => 'comave.com',
        'comave-magento.ddev.site' => 'dev.comave.com',
        'mcdev.comave.com' => 'development.comave.com'
    ];

    /**
     * @param Config $config
     * @param UrlInterface $urlBuilder
     * @param DeploymentConfig $deploymentConfig
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly Config $config,
        private readonly UrlInterface $urlBuilder,
        private readonly DeploymentConfig $deploymentConfig,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * After plugin for urlDirective method to rewrite URLs to frontend domain
     *
     * @param Filter $subject
     * @param string $result
     * @param array $construction
     * @return string
     */
    public function afterUrlDirective(Filter $subject, string $result, array $construction): string
    {
        $this->logger->info('EmailTemplateUrlPlugin: Processing URL directive', [
            'original_result' => $result,
            'construction' => $construction
        ]);

        // Get frontend base URL
        $frontendBaseUrl = $this->getFrontendBaseUrl();
        
        if (empty($frontendBaseUrl)) {
            $this->logger->warning('EmailTemplateUrlPlugin: Frontend base URL is empty, returning original result');
            return $result;
        }

        $this->logger->info('EmailTemplateUrlPlugin: Frontend base URL found', [
            'frontend_base_url' => $frontendBaseUrl
        ]);

        // Remove trailing slash for consistent replacement
        $frontendBaseUrl = rtrim($frontendBaseUrl, '/');

        $originalResult = $result;

        // Replace backend domain URLs with frontend domain URLs
        foreach (self::BACKEND_DOMAINS as $backendDomain) {
            $patterns = [
                'https://' . $backendDomain,
                'http://' . $backendDomain,
            ];

            foreach ($patterns as $pattern) {
                if (strpos($result, $pattern) !== false) {
                    $result = str_replace($pattern, $frontendBaseUrl, $result);
                    $this->logger->info('EmailTemplateUrlPlugin: URL replacement performed', [
                        'pattern' => $pattern,
                        'frontend_url' => $frontendBaseUrl,
                        'before' => $originalResult,
                        'after' => $result
                    ]);
                }
            }
        }

        if ($originalResult !== $result) {
            $this->logger->info('EmailTemplateUrlPlugin: Final URL transformation', [
                'original' => $originalResult,
                'transformed' => $result
            ]);
        } else {
            $this->logger->info('EmailTemplateUrlPlugin: No URL transformation needed', [
                'url' => $result
            ]);
        }

        return $result;
    }

    /**
     * Get frontend base URL
     *
     * @return string
     */
    private function getFrontendBaseUrl(): string
    {
        // First try to get from env.php deployment configuration
        $envFrontendUrl = $this->deploymentConfig->get('frontend_base_url');

        $this->logger->info('EmailTemplateUrlPlugin: Checking env.php config', [
            'env_frontend_base_url' => $envFrontendUrl
        ]);

        if (!empty($envFrontendUrl)) {
            $this->logger->info('EmailTemplateUrlPlugin: Using env.php frontend URL', [
                'url' => $envFrontendUrl
            ]);
            return $envFrontendUrl;
        }

        // Second try to get from custom frontend configuration
        $frontendUrl = $this->config->getFrontendSecureUrl(ScopeInterface::SCOPE_DEFAULT);

        $this->logger->info('EmailTemplateUrlPlugin: Checking frontend config', [
            'frontend_secure_url' => $frontendUrl
        ]);

        if (!empty($frontendUrl) && $frontendUrl !== '{{secure_base_url}}') {
            $this->logger->info('EmailTemplateUrlPlugin: Using frontend config URL', [
                'url' => $frontendUrl
            ]);
            return $frontendUrl;
        }

        // Fallback to mapping based on current backend domain
        $currentBaseUrl = $this->urlBuilder->getBaseUrl();
        $currentDomain = parse_url($currentBaseUrl, PHP_URL_HOST);
        
        $this->logger->info('EmailTemplateUrlPlugin: Checking domain mapping', [
            'current_base_url' => $currentBaseUrl,
            'current_domain' => $currentDomain,
            'available_mappings' => self::FRONTEND_DOMAIN_MAPPING
        ]);
        
        if ($currentDomain && isset(self::FRONTEND_DOMAIN_MAPPING[$currentDomain])) {
            $frontendDomain = self::FRONTEND_DOMAIN_MAPPING[$currentDomain];
            $scheme = parse_url($currentBaseUrl, PHP_URL_SCHEME) ?: 'https';
            $mappedUrl = $scheme . '://' . $frontendDomain . '/';
            
            $this->logger->info('EmailTemplateUrlPlugin: Using domain mapping', [
                'backend_domain' => $currentDomain,
                'frontend_domain' => $frontendDomain,
                'mapped_url' => $mappedUrl
            ]);
            
            return $mappedUrl;
        }

        $this->logger->warning('EmailTemplateUrlPlugin: No frontend URL found', [
            'current_domain' => $currentDomain,
            'frontend_config' => $frontendUrl
        ]);

        return '';
    }
}
